/**
*@file LaneDetector.cpp
*<AUTHOR>
*@brief Definition of all the function that form part of the LaneDetector class.
*@brief The class will take RGB images as inputs and will output the same RGB image but
*@brief with the plot of the detected lanes and the turn prediction.
*/
#include <string>
#include <vector>
#include <opencv2/opencv.hpp>   
#include "LaneDetector.h"

// IMAGE BLURRING
/**
*@brief Apply gaussian filter to the input image to denoise it
*@param inputImage is the frame of a video in which the
*@param lane is going to be detected
*@return Blurred and denoised image
*/
cv::Mat LaneDetector::deNoise(cv::Mat inputImage) 
{
    cv::Mat output;

    cv::GaussianBlur(inputImage, output, cv::Size(3, 3), 0, 0);

    return output;
}

// COLOR SPACE PROCESSING
/**
*@brief Process image in HSV and HLS color spaces to better detect yellow and white lane lines
*@param inputImage is the original RGB image
*@return Binary mask highlighting potential lane lines based on color
*/
cv::Mat LaneDetector::colorThreshold(cv::Mat inputImage)
{
    cv::Mat hsv, hls, white_mask, yellow_mask, combined_mask;

    // Convert to HSV and HLS color spaces
    cv::cvtColor(inputImage, hsv, cv::COLOR_RGB2HSV);
    cv::cvtColor(inputImage, hls, cv::COLOR_RGB2HLS);

    // Define range for white color in HLS
    cv::Scalar white_lower(0, 200, 0);
    cv::Scalar white_upper(255, 255, 255);
    cv::inRange(hls, white_lower, white_upper, white_mask);

    // Define range for yellow color in HSV
    cv::Scalar yellow_lower(20, 100, 100);
    cv::Scalar yellow_upper(30, 255, 255);
    cv::inRange(hsv, yellow_lower, yellow_upper, yellow_mask);

    // Combine white and yellow masks
    cv::bitwise_or(white_mask, yellow_mask, combined_mask);

    return combined_mask;
}

// ADAPTIVE THRESHOLD CALCULATION
/**
*@brief Calculate adaptive thresholds for Canny edge detection based on image statistics
*@param image is the grayscale image
*@return Pair of low and high thresholds
*/
std::pair<double, double> LaneDetector::calculateAdaptiveThresholds(cv::Mat image)
{
    // Calculate image statistics
    cv::Scalar mean, stddev;
    cv::meanStdDev(image, mean, stddev);

    double sigma = 0.33;
    double median = mean[0]; // Approximation using mean

    // Calculate adaptive thresholds
    double low_threshold = std::max(0.0, (1.0 - sigma) * median);
    double high_threshold = std::min(255.0, (1.0 + sigma) * median);

    // Ensure minimum threshold values for lane detection
    low_threshold = std::max(low_threshold, 50.0);
    high_threshold = std::max(high_threshold, 100.0);

    return std::make_pair(low_threshold, high_threshold);
}

// EDGE DETECTION
/**
*@brief Detect all the edges in the blurred frame using improved Canny edge detection
*@param img_noise is the previously blurred frame
*@return Binary image with only the edges represented in white
*/
cv::Mat LaneDetector::edgeDetector(cv::Mat img_noise)
{
    cv::Mat gray, edges, combined_edges;

    // Convert image from RGB to gray
    cv::cvtColor(img_noise, gray, cv::COLOR_RGB2GRAY);

    // Calculate adaptive thresholds based on image statistics
    auto thresholds = calculateAdaptiveThresholds(gray);
    double low_threshold = thresholds.first;
    double high_threshold = thresholds.second;

    // Apply Canny edge detection with adaptive thresholds
    cv::Canny(gray, edges, low_threshold, high_threshold, 3);

    // Get color-based lane detection
    cv::Mat color_mask = colorThreshold(img_noise);

    // Combine edge detection with color detection
    cv::bitwise_or(edges, color_mask, combined_edges);

    // Apply morphological operations to clean up the edges
    cv::Mat kernel = cv::getStructuringElement(cv::MORPH_RECT, cv::Size(3, 3));
    cv::morphologyEx(combined_edges, combined_edges, cv::MORPH_CLOSE, kernel);

    cv::imshow("edges", combined_edges);
    return combined_edges;
}

// MASK THE EDGE IMAGE
/**
*@brief Mask the image so that only the edges that form part of the lane are detected
*@brief Uses dynamic ROI based on image dimensions for better adaptability
*@param img_edges is the edges image from the previous function
*@return Binary image with only the desired edges being represented
*/
cv::Mat LaneDetector::mask(cv::Mat img_edges)
{
    cv::Mat output;
    cv::Mat mask = cv::Mat::zeros(img_edges.size(), img_edges.type());

    // Get image dimensions
    int height = img_edges.rows;
    int width = img_edges.cols;

    // Define ROI vertices as ratios of image dimensions for better adaptability
    cv::Point pts[4] = {
        cv::Point(static_cast<int>(width * 0.15), height),                    // Bottom left
        cv::Point(static_cast<int>(width * 0.45), static_cast<int>(height * 0.6)),  // Top left
        cv::Point(static_cast<int>(width * 0.55), static_cast<int>(height * 0.6)),  // Top right
        cv::Point(static_cast<int>(width * 0.95), height)                     // Bottom right
    };

    // Create a binary polygon mask
    cv::fillConvexPoly(mask, pts, 4, cv::Scalar(255, 0, 0));
    // Multiply the edges image and the mask to get the output
    cv::bitwise_and(img_edges, mask, output);

    return output;
}

// HOUGH LINES
/**
*@brief Obtain all the line segments in the masked images using adaptive parameters
*@param img_mask is the masked binary image from the previous function
*@return Vector that contains all the detected lines in the image
*/
std::vector<cv::Vec4i> LaneDetector::houghLines(cv::Mat img_mask)
{
    std::vector<cv::Vec4i> line;

    // Calculate adaptive parameters based on image size and content
    int height = img_mask.rows;
    int width = img_mask.cols;

    // Adaptive threshold based on image size
    int threshold = std::max(30, static_cast<int>(height * 0.05));

    // Adaptive minimum line length
    int min_line_length = std::max(20, static_cast<int>(height * 0.03));

    // Adaptive maximum line gap
    int max_line_gap = std::max(10, static_cast<int>(height * 0.02));

    // Apply Hough transform with adaptive parameters
    cv::HoughLinesP(img_mask, line, 1, CV_PI / 180, threshold, min_line_length, max_line_gap);

    // If too few lines detected, try with relaxed parameters
    if (line.size() < 5) {
        line.clear();
        cv::HoughLinesP(img_mask, line, 1, CV_PI / 180, threshold * 0.7, min_line_length * 0.7, max_line_gap * 1.5);
    }

    return line;
}

// SORT RIGHT AND LEFT LINES
/**
*@brief Sort all the detected Hough lines by slope with improved filtering
*@brief The lines are classified into right or left depending
*@brief on the sign of their slope and their approximate location
*@param lines is the vector that contains all the detected lines
*@param img_edges is used for determining the image center
*@return The output is a vector(2) that contains all the classified lines
*/
std::vector<std::vector<cv::Vec4i> > LaneDetector::lineSeparation(std::vector<cv::Vec4i> lines, cv::Mat img_edges)
{
    std::vector<std::vector<cv::Vec4i> > output(2);
    cv::Point ini, fini;
    double slope_thresh_min = 0.3;  // Minimum slope threshold
    double slope_thresh_max = 3.0;  // Maximum slope threshold to filter out vertical lines
    std::vector<double> slopes;
    std::vector<cv::Vec4i> selected_lines;
    std::vector<cv::Vec4i> right_lines, left_lines;

    // Reset flags
    left_flag = false;
    right_flag = false;

    // Calculate the slope of all the detected lines with improved filtering
    for (const auto& line : lines) {
        ini = cv::Point(line[0], line[1]);
        fini = cv::Point(line[2], line[3]);

        // Calculate line length for filtering short lines
        double line_length = cv::norm(fini - ini);
        if (line_length < 30) continue;  // Filter out very short lines

        // Basic algebra: slope = (y1 - y0)/(x1 - x0)
        double dx = static_cast<double>(fini.x) - static_cast<double>(ini.x);
        double dy = static_cast<double>(fini.y) - static_cast<double>(ini.y);

        if (std::abs(dx) < 0.001) continue;  // Avoid division by zero

        double slope = dy / dx;

        // Filter lines by slope range
        if (std::abs(slope) > slope_thresh_min && std::abs(slope) < slope_thresh_max) {
            slopes.push_back(slope);
            selected_lines.push_back(line);
        }
    }

    // Split the lines into right and left lines
    img_center = static_cast<double>(img_edges.cols / 2);

    for (size_t j = 0; j < selected_lines.size(); j++) {
        ini = cv::Point(selected_lines[j][0], selected_lines[j][1]);
        fini = cv::Point(selected_lines[j][2], selected_lines[j][3]);

        // Calculate line center for better classification
        double line_center_x = (ini.x + fini.x) / 2.0;

        // Improved condition to classify line as left side or right side
        if (slopes[j] > 0 && line_center_x > img_center * 0.6) {
            right_lines.push_back(selected_lines[j]);
            right_flag = true;
        }
        else if (slopes[j] < 0 && line_center_x < img_center * 1.4) {
            left_lines.push_back(selected_lines[j]);
            left_flag = true;
        }
    }

    output[0] = right_lines;
    output[1] = left_lines;

    return output;
}

// REGRESSION FOR LEFT AND RIGHT LINES
/**
*@brief Regression takes all the classified line segments and fits lines using least squares
*@brief Includes multi-frame stability for smoother lane detection
*@param left_right_lines is the output of the lineSeparation function
*@param inputImage is used to select where do the lines will end
*@return output contains the initial and final points of both lane boundary lines
*/
std::vector<cv::Point> LaneDetector::regression(std::vector<std::vector<cv::Vec4i> > left_right_lines, cv::Mat inputImage)
{
    std::vector<cv::Point> output(4);
    cv::Point ini, fini;
    cv::Vec4d right_line, left_line;
    std::vector<cv::Point> right_pts, left_pts;

    frame_count++;
    double smoothing_factor = 0.7;  // For temporal smoothing

    // Process right lines
    if (right_flag == true) {
        for (const auto& line : left_right_lines[0]) {
            ini = cv::Point(line[0], line[1]);
            fini = cv::Point(line[2], line[3]);
            right_pts.push_back(ini);
            right_pts.push_back(fini);
        }

        if (right_pts.size() > 0) {
            cv::fitLine(right_pts, right_line, cv::DIST_L2, 0, 0.01, 0.01);

            // Calculate new line parameters
            double new_right_m = right_line[1] / (right_line[0] + 1e-6);
            cv::Point new_right_b = cv::Point(right_line[2], right_line[3]);

            // Apply temporal smoothing if we have previous frame data
            if (frame_count > 1 && !prev_right_line.empty()) {
                right_m = smoothing_factor * right_m + (1 - smoothing_factor) * new_right_m;
                right_b.x = smoothing_factor * right_b.x + (1 - smoothing_factor) * new_right_b.x;
                right_b.y = smoothing_factor * right_b.y + (1 - smoothing_factor) * new_right_b.y;
            } else {
                right_m = new_right_m;
                right_b = new_right_b;
            }
        }
    }

    // Process left lines
    if (left_flag == true) {
        for (const auto& line : left_right_lines[1]) {
            ini = cv::Point(line[0], line[1]);
            fini = cv::Point(line[2], line[3]);
            left_pts.push_back(ini);
            left_pts.push_back(fini);
        }

        if (left_pts.size() > 0) {
            cv::fitLine(left_pts, left_line, cv::DIST_L2, 0, 0.01, 0.01);

            // Calculate new line parameters
            double new_left_m = left_line[1] / (left_line[0] + 1e-6);
            cv::Point new_left_b = cv::Point(left_line[2], left_line[3]);

            // Apply temporal smoothing if we have previous frame data
            if (frame_count > 1 && !prev_left_line.empty()) {
                left_m = smoothing_factor * left_m + (1 - smoothing_factor) * new_left_m;
                left_b.x = smoothing_factor * left_b.x + (1 - smoothing_factor) * new_left_b.x;
                left_b.y = smoothing_factor * left_b.y + (1 - smoothing_factor) * new_left_b.y;
            } else {
                left_m = new_left_m;
                left_b = new_left_b;
            }
        }
    }

    // Calculate line endpoints
    int ini_y = inputImage.rows;
    int fin_y = static_cast<int>(inputImage.rows * 0.6);  // Dynamic based on image height

    // Calculate line points with bounds checking
    double right_ini_x = std::abs(right_m) > 1e-6 ? ((ini_y - right_b.y) / right_m) + right_b.x : right_b.x;
    double right_fin_x = std::abs(right_m) > 1e-6 ? ((fin_y - right_b.y) / right_m) + right_b.x : right_b.x;
    double left_ini_x = std::abs(left_m) > 1e-6 ? ((ini_y - left_b.y) / left_m) + left_b.x : left_b.x;
    double left_fin_x = std::abs(left_m) > 1e-6 ? ((fin_y - left_b.y) / left_m) + left_b.x : left_b.x;

    // Clamp values to image bounds
    right_ini_x = std::max(0.0, std::min(static_cast<double>(inputImage.cols), right_ini_x));
    right_fin_x = std::max(0.0, std::min(static_cast<double>(inputImage.cols), right_fin_x));
    left_ini_x = std::max(0.0, std::min(static_cast<double>(inputImage.cols), left_ini_x));
    left_fin_x = std::max(0.0, std::min(static_cast<double>(inputImage.cols), left_fin_x));

    output[0] = cv::Point(static_cast<int>(right_ini_x), ini_y);
    output[1] = cv::Point(static_cast<int>(right_fin_x), fin_y);
    output[2] = cv::Point(static_cast<int>(left_ini_x), ini_y);
    output[3] = cv::Point(static_cast<int>(left_fin_x), fin_y);

    // Store current frame data for next frame smoothing
    prev_right_line = {output[0], output[1]};
    prev_left_line = {output[2], output[3]};

    return output;
}

// TURN PREDICTION
/**
*@brief Predict if the lane is turning left, right or if it is going straight
*@brief It is done by seeing where the vanishing point is with respect to the center of the image
*@return String that says if there is left or right turn or if the road is straight
*/
std::string LaneDetector::predictTurn() 
{
    std::string output;
    double vanish_x;
    double thr_vp = 10;

    // The vanishing point is the point where both lane boundary lines intersect
    vanish_x = static_cast<double>(((right_m*right_b.x) - (left_m*left_b.x) - right_b.y + left_b.y) / (right_m - left_m));

    // The vanishing points location determines where is the road turning
    if (vanish_x < (img_center - thr_vp))
        output = "Turn left";
    else if (vanish_x >(img_center + thr_vp))
        output = "Turn right";
    else if (vanish_x >= (img_center - thr_vp) && vanish_x <= (img_center + thr_vp))
        output = "Straight";

    return output;
}

// PLOT RESULTS
/**
*@brief This function plots both sides of the lane, the turn prediction message and a transparent polygon that covers the area inside the lane boundaries
*@param inputImage is the original captured frame
*@param lane is the vector containing the information of both lines
*@param turn is the output string containing the turn information
*@return The function returns a 0
*/
int LaneDetector::plotLane(cv::Mat inputImage, std::vector<cv::Point> lane, std::string turn) 
{
    std::vector<cv::Point> poly_points;
    cv::Mat output;

    // Create the transparent polygon for a better visualization of the lane
    inputImage.copyTo(output);
    poly_points.push_back(lane[2]);
    poly_points.push_back(lane[0]);
    poly_points.push_back(lane[1]);
    poly_points.push_back(lane[3]);
    cv::fillConvexPoly(output, poly_points, cv::Scalar(0, 0, 255), cv::LINE_AA, 0);
    cv::addWeighted(output, 0.3, inputImage, 1.0 - 0.3, 0, inputImage);

    // Plot both lines of the lane boundary
    cv::line(inputImage, lane[0], lane[1], cv::Scalar(0, 255, 255), 5, cv::LINE_AA);
    cv::line(inputImage, lane[2], lane[3], cv::Scalar(0, 255, 255), 5, cv::LINE_AA);

    // Plot the turn message
    cv::putText(inputImage, turn, cv::Point(50, 90), cv::FONT_HERSHEY_COMPLEX_SMALL, 3, cv::Scalar(0, 255, 0), 1, cv::LINE_AA);

    // Show the final output image
    cv::namedWindow("Lane", cv::WINDOW_AUTOSIZE);
    cv::imshow("Lane", inputImage);
    return 0;
}