/**
*@file LaneDetector.cpp
*<AUTHOR>
*@brief Definition of all the function that form part of the LaneDetector class.
*@brief The class will take RGB images as inputs and will output the same RGB image but
*@brief with the plot of the detected lanes and the turn prediction.
*/
#include <string>
#include <vector>
#include <opencv2/opencv.hpp>   
#include "LaneDetector.h"

// IMAGE BLURRING
/**
*@brief Apply gaussian filter to the input image to denoise it
*@param inputImage is the frame of a video in which the
*@param lane is going to be detected
*@return Blurred and denoised image
*/
cv::Mat LaneDetector::deNoise(cv::Mat inputImage) 
{
    cv::Mat output;

    cv::GaussianBlur(inputImage, output, cv::Size(3, 3), 0, 0);

    return output;
}

// COLOR SPACE PROCESSING
/**
*@brief Process image in HSV and HLS color spaces to better detect yellow and white lane lines
*@brief Improved algorithm with multiple color space analysis and adaptive thresholding
*@param inputImage is the original RGB image
*@return Binary mask highlighting potential lane lines based on color
*/
cv::Mat LaneDetector::colorThreshold(cv::Mat inputImage)
{
    cv::Mat hsv, hls, lab, gray;
    cv::Mat white_mask, yellow_mask, white_mask_hls, white_mask_lab;
    cv::Mat yellow_mask_hsv, yellow_mask_lab;
    cv::Mat combined_mask;

    // Convert to different color spaces for robust detection
    cv::cvtColor(inputImage, hsv, cv::COLOR_BGR2HSV);
    cv::cvtColor(inputImage, hls, cv::COLOR_BGR2HLS);
    cv::cvtColor(inputImage, lab, cv::COLOR_BGR2Lab);
    cv::cvtColor(inputImage, gray, cv::COLOR_BGR2GRAY);

    // === WHITE LINE DETECTION ===

    // Method 1: HLS color space for white detection (better for varying lighting)
    cv::Scalar white_lower_hls(0, 200, 0);
    cv::Scalar white_upper_hls(180, 255, 255);
    cv::inRange(hls, white_lower_hls, white_upper_hls, white_mask_hls);

    // Method 2: LAB color space L channel for white detection
    std::vector<cv::Mat> lab_channels;
    cv::split(lab, lab_channels);
    cv::Mat l_channel = lab_channels[0];
    cv::threshold(l_channel, white_mask_lab, 215, 255, cv::THRESH_BINARY);

    // Method 3: Grayscale threshold for bright areas
    cv::Mat white_mask_gray;
    cv::threshold(gray, white_mask_gray, 200, 255, cv::THRESH_BINARY);

    // Combine white detection methods
    cv::bitwise_or(white_mask_hls, white_mask_lab, white_mask);
    cv::bitwise_or(white_mask, white_mask_gray, white_mask);

    // === YELLOW LINE DETECTION ===

    // Method 1: HSV color space for yellow detection (more precise hue range)
    cv::Scalar yellow_lower_hsv(15, 80, 80);   // Broader range for different lighting
    cv::Scalar yellow_upper_hsv(35, 255, 255);
    cv::inRange(hsv, yellow_lower_hsv, yellow_upper_hsv, yellow_mask_hsv);

    // Method 2: LAB color space for yellow detection (B channel)
    cv::Mat b_channel = lab_channels[2];
    cv::Mat yellow_mask_lab_b;
    cv::threshold(b_channel, yellow_mask_lab_b, 145, 255, cv::THRESH_BINARY);

    // Method 3: Additional HSV range for pale yellow
    cv::Mat yellow_mask_pale;
    cv::Scalar yellow_pale_lower(10, 50, 100);
    cv::Scalar yellow_pale_upper(40, 180, 255);
    cv::inRange(hsv, yellow_pale_lower, yellow_pale_upper, yellow_mask_pale);

    // Combine yellow detection methods
    cv::bitwise_or(yellow_mask_hsv, yellow_mask_lab_b, yellow_mask);
    cv::bitwise_or(yellow_mask, yellow_mask_pale, yellow_mask);

    // === COMBINE AND REFINE ===

    // Combine white and yellow masks
    cv::bitwise_or(white_mask, yellow_mask, combined_mask);

    // Apply morphological operations to clean up the mask
    cv::Mat kernel_open = cv::getStructuringElement(cv::MORPH_ELLIPSE, cv::Size(3, 3));
    cv::Mat kernel_close = cv::getStructuringElement(cv::MORPH_ELLIPSE, cv::Size(5, 5));

    // Remove small noise
    cv::morphologyEx(combined_mask, combined_mask, cv::MORPH_OPEN, kernel_open);
    // Fill small gaps
    cv::morphologyEx(combined_mask, combined_mask, cv::MORPH_CLOSE, kernel_close);

    // Apply Gaussian blur to smooth the mask
    cv::GaussianBlur(combined_mask, combined_mask, cv::Size(5, 5), 0);
    cv::threshold(combined_mask, combined_mask, 127, 255, cv::THRESH_BINARY);

    // Debug: show color detection results
    cv::imshow("White Mask", white_mask);
    cv::imshow("Yellow Mask", yellow_mask);
    cv::imshow("Combined Color Mask", combined_mask);

    return combined_mask;
}

// ADAPTIVE THRESHOLD CALCULATION
/**
*@brief Calculate adaptive thresholds for Canny edge detection based on image statistics
*@param image is the grayscale image
*@return Pair of low and high thresholds
*/
std::pair<double, double> LaneDetector::calculateAdaptiveThresholds(cv::Mat image)
{
    // Calculate image statistics
    cv::Scalar mean, stddev;
    cv::meanStdDev(image, mean, stddev);

    double sigma = 0.33;
    double median = mean[0]; // Approximation using mean

    // Calculate adaptive thresholds
    double low_threshold = std::max(0.0, (1.0 - sigma) * median);
    double high_threshold = std::min(255.0, (1.0 + sigma) * median);

    // Ensure minimum threshold values for lane detection
    low_threshold = std::max(low_threshold, 50.0);
    high_threshold = std::max(high_threshold, 100.0);

    return std::make_pair(low_threshold, high_threshold);
}

// EDGE DETECTION
/**
*@brief Detect all the edges in the blurred frame using improved Canny edge detection
*@param img_noise is the previously blurred frame
*@return Binary image with only the edges represented in white
*/
cv::Mat LaneDetector::edgeDetector(cv::Mat img_noise)
{
    cv::Mat gray, edges, combined_edges, enhanced_edges;

    // Convert image from BGR to gray
    cv::cvtColor(img_noise, gray, cv::COLOR_BGR2GRAY);

    // === 多层次边缘检测策略 ===

    // 1. 获取基于颜色的车道线检测（主要方法）
    cv::Mat color_mask = colorThreshold(img_noise);

    // 2. 应用自适应直方图均衡化增强对比度
    cv::Mat enhanced_gray;
    cv::createCLAHE(2.0, cv::Size(8, 8))->apply(gray, enhanced_gray);

    // 3. 计算自适应阈值
    auto thresholds = calculateAdaptiveThresholds(enhanced_gray);
    double low_threshold = thresholds.first;
    double high_threshold = thresholds.second;

    // 4. 多尺度Canny边缘检测
    cv::Mat edges1, edges2, edges3;

    // 细节边缘检测（用于虚线）
    cv::Canny(enhanced_gray, edges1, low_threshold * 0.5, high_threshold * 0.7, 3);

    // 标准边缘检测
    cv::Canny(enhanced_gray, edges2, low_threshold, high_threshold, 3);

    // 强边缘检测（用于实线）
    cv::Canny(enhanced_gray, edges3, low_threshold * 1.5, high_threshold * 1.3, 3);

    // 合并不同尺度的边缘
    cv::bitwise_or(edges1, edges2, edges);
    cv::bitwise_or(edges, edges3, edges);

    // 5. 方向性滤波 - 只保留接近垂直的边缘（车道线特征）
    cv::Mat sobel_x, sobel_y, sobel_combined;
    cv::Sobel(enhanced_gray, sobel_x, CV_64F, 1, 0, 3);
    cv::Sobel(enhanced_gray, sobel_y, CV_64F, 0, 1, 3);

    // 计算梯度方向，只保留接近垂直的边缘
    cv::Mat direction_mask = cv::Mat::zeros(gray.size(), CV_8UC1);
    for (int i = 0; i < sobel_x.rows; i++) {
        for (int j = 0; j < sobel_x.cols; j++) {
            double dx = sobel_x.at<double>(i, j);
            double dy = sobel_y.at<double>(i, j);
            double angle = std::atan2(std::abs(dy), std::abs(dx)) * 180.0 / CV_PI;

            // 保留角度在20-70度之间的边缘（车道线的典型角度）
            if (angle > 20 && angle < 70) {
                direction_mask.at<uchar>(i, j) = 255;
            }
        }
    }

    // 将方向滤波应用到边缘检测结果
    cv::bitwise_and(edges, direction_mask, enhanced_edges);

    // 6. 结合颜色检测和边缘检测
    // 颜色检测权重更高，因为它更准确
    cv::Mat weighted_color, weighted_edges;
    color_mask.convertTo(weighted_color, CV_8UC1, 0.8);  // 颜色检测权重80%
    enhanced_edges.convertTo(weighted_edges, CV_8UC1, 0.3);  // 边缘检测权重30%

    cv::add(weighted_color, weighted_edges, combined_edges);
    cv::threshold(combined_edges, combined_edges, 127, 255, cv::THRESH_BINARY);

    // 7. 形态学操作优化
    // 针对虚线的连接操作
    cv::Mat kernel_line = cv::getStructuringElement(cv::MORPH_RECT, cv::Size(1, 15));
    cv::morphologyEx(combined_edges, combined_edges, cv::MORPH_CLOSE, kernel_line);

    // 去除小噪声
    cv::Mat kernel_noise = cv::getStructuringElement(cv::MORPH_ELLIPSE, cv::Size(3, 3));
    cv::morphologyEx(combined_edges, combined_edges, cv::MORPH_OPEN, kernel_noise);

    // 调试显示
    cv::imshow("Enhanced Gray", enhanced_gray);
    cv::imshow("Direction Mask", direction_mask);
    cv::imshow("Color Detection", color_mask);
    cv::imshow("Edge Detection", enhanced_edges);
    cv::imshow("Final Combined", combined_edges);

    return combined_edges;
}

// MASK THE EDGE IMAGE
/**
*@brief Mask the image so that only the edges that form part of the lane are detected
*@brief Uses dynamic ROI based on image dimensions for better adaptability
*@param img_edges is the edges image from the previous function
*@return Binary image with only the desired edges being represented
*/
cv::Mat LaneDetector::mask(cv::Mat img_edges)
{
    cv::Mat output;
    cv::Mat mask = cv::Mat::zeros(img_edges.size(), img_edges.type());

    // Get image dimensions
    int height = img_edges.rows;
    int width = img_edges.cols;

    // Define ROI vertices as ratios of image dimensions for better adaptability
    cv::Point pts[4] = {
        cv::Point(static_cast<int>(width * 0.15), height),                    // Bottom left
        cv::Point(static_cast<int>(width * 0.45), static_cast<int>(height * 0.6)),  // Top left
        cv::Point(static_cast<int>(width * 0.55), static_cast<int>(height * 0.6)),  // Top right
        cv::Point(static_cast<int>(width * 0.95), height)                     // Bottom right
    };

    // Create a binary polygon mask
    cv::fillConvexPoly(mask, pts, 4, cv::Scalar(255, 0, 0));
    // Multiply the edges image and the mask to get the output
    cv::bitwise_and(img_edges, mask, output);

    return output;
}

// HOUGH LINES
/**
*@brief Obtain all the line segments in the masked images using adaptive parameters
*@param img_mask is the masked binary image from the previous function
*@return Vector that contains all the detected lines in the image
*/
std::vector<cv::Vec4i> LaneDetector::houghLines(cv::Mat img_mask)
{
    std::vector<cv::Vec4i> line;

    // Calculate adaptive parameters based on image size and content
    int height = img_mask.rows;
    int width = img_mask.cols;

    // Adaptive threshold based on image size
    int threshold = std::max(30, static_cast<int>(height * 0.05));

    // Adaptive minimum line length
    int min_line_length = std::max(20, static_cast<int>(height * 0.03));

    // Adaptive maximum line gap
    int max_line_gap = std::max(10, static_cast<int>(height * 0.02));

    // Apply Hough transform with adaptive parameters
    cv::HoughLinesP(img_mask, line, 1, CV_PI / 180, threshold, min_line_length, max_line_gap);

    // If too few lines detected, try with relaxed parameters
    if (line.size() < 5) {
        line.clear();
        cv::HoughLinesP(img_mask, line, 1, CV_PI / 180, threshold * 0.7, min_line_length * 0.7, max_line_gap * 1.5);
    }

    return line;
}

// SORT RIGHT AND LEFT LINES
/**
*@brief Sort all the detected Hough lines by slope with improved filtering
*@brief The lines are classified into right or left depending
*@brief on the sign of their slope and their approximate location
*@param lines is the vector that contains all the detected lines
*@param img_edges is used for determining the image center
*@return The output is a vector(2) that contains all the classified lines
*/
std::vector<std::vector<cv::Vec4i> > LaneDetector::lineSeparation(std::vector<cv::Vec4i> lines, cv::Mat img_edges)
{
    std::vector<std::vector<cv::Vec4i> > output(2);
    cv::Point ini, fini;
    double slope_thresh_min = 0.3;  // Minimum slope threshold
    double slope_thresh_max = 3.0;  // Maximum slope threshold to filter out vertical lines
    std::vector<double> slopes;
    std::vector<cv::Vec4i> selected_lines;
    std::vector<cv::Vec4i> right_lines, left_lines;

    // Reset flags
    left_flag = false;
    right_flag = false;

    // Calculate the slope of all the detected lines with improved filtering
    for (const auto& line : lines) {
        ini = cv::Point(line[0], line[1]);
        fini = cv::Point(line[2], line[3]);

        // Calculate line length for filtering short lines
        double line_length = cv::norm(fini - ini);
        if (line_length < 30) continue;  // Filter out very short lines

        // Basic algebra: slope = (y1 - y0)/(x1 - x0)
        double dx = static_cast<double>(fini.x) - static_cast<double>(ini.x);
        double dy = static_cast<double>(fini.y) - static_cast<double>(ini.y);

        if (std::abs(dx) < 0.001) continue;  // Avoid division by zero

        double slope = dy / dx;

        // Filter lines by slope range
        if (std::abs(slope) > slope_thresh_min && std::abs(slope) < slope_thresh_max) {
            slopes.push_back(slope);
            selected_lines.push_back(line);
        }
    }

    // Split the lines into right and left lines
    img_center = static_cast<double>(img_edges.cols / 2);

    for (size_t j = 0; j < selected_lines.size(); j++) {
        ini = cv::Point(selected_lines[j][0], selected_lines[j][1]);
        fini = cv::Point(selected_lines[j][2], selected_lines[j][3]);

        // Calculate line center for better classification
        double line_center_x = (ini.x + fini.x) / 2.0;

        // Improved condition to classify line as left side or right side
        if (slopes[j] > 0 && line_center_x > img_center * 0.6) {
            right_lines.push_back(selected_lines[j]);
            right_flag = true;
        }
        else if (slopes[j] < 0 && line_center_x < img_center * 1.4) {
            left_lines.push_back(selected_lines[j]);
            left_flag = true;
        }
    }

    output[0] = right_lines;
    output[1] = left_lines;

    return output;
}

// REGRESSION FOR LEFT AND RIGHT LINES
/**
*@brief Regression takes all the classified line segments and fits lines using least squares
*@brief Includes multi-frame stability for smoother lane detection
*@param left_right_lines is the output of the lineSeparation function
*@param inputImage is used to select where do the lines will end
*@return output contains the initial and final points of both lane boundary lines
*/
std::vector<cv::Point> LaneDetector::regression(std::vector<std::vector<cv::Vec4i> > left_right_lines, cv::Mat inputImage)
{
    std::vector<cv::Point> output(4);
    cv::Point ini, fini;
    cv::Vec4d right_line, left_line;
    std::vector<cv::Point> right_pts, left_pts;

    frame_count++;
    double smoothing_factor = 0.7;  // For temporal smoothing

    // Process right lines
    if (right_flag == true) {
        for (const auto& line : left_right_lines[0]) {
            ini = cv::Point(line[0], line[1]);
            fini = cv::Point(line[2], line[3]);
            right_pts.push_back(ini);
            right_pts.push_back(fini);
        }

        if (right_pts.size() > 0) {
            cv::fitLine(right_pts, right_line, cv::DIST_L2, 0, 0.01, 0.01);

            // Calculate new line parameters
            double new_right_m = right_line[1] / (right_line[0] + 1e-6);
            cv::Point new_right_b = cv::Point(right_line[2], right_line[3]);

            // Apply temporal smoothing if we have previous frame data
            if (frame_count > 1 && !prev_right_line.empty()) {
                right_m = smoothing_factor * right_m + (1 - smoothing_factor) * new_right_m;
                right_b.x = smoothing_factor * right_b.x + (1 - smoothing_factor) * new_right_b.x;
                right_b.y = smoothing_factor * right_b.y + (1 - smoothing_factor) * new_right_b.y;
            } else {
                right_m = new_right_m;
                right_b = new_right_b;
            }
        }
    }

    // Process left lines
    if (left_flag == true) {
        for (const auto& line : left_right_lines[1]) {
            ini = cv::Point(line[0], line[1]);
            fini = cv::Point(line[2], line[3]);
            left_pts.push_back(ini);
            left_pts.push_back(fini);
        }

        if (left_pts.size() > 0) {
            cv::fitLine(left_pts, left_line, cv::DIST_L2, 0, 0.01, 0.01);

            // Calculate new line parameters
            double new_left_m = left_line[1] / (left_line[0] + 1e-6);
            cv::Point new_left_b = cv::Point(left_line[2], left_line[3]);

            // Apply temporal smoothing if we have previous frame data
            if (frame_count > 1 && !prev_left_line.empty()) {
                left_m = smoothing_factor * left_m + (1 - smoothing_factor) * new_left_m;
                left_b.x = smoothing_factor * left_b.x + (1 - smoothing_factor) * new_left_b.x;
                left_b.y = smoothing_factor * left_b.y + (1 - smoothing_factor) * new_left_b.y;
            } else {
                left_m = new_left_m;
                left_b = new_left_b;
            }
        }
    }

    // Calculate line endpoints
    int ini_y = inputImage.rows;
    int fin_y = static_cast<int>(inputImage.rows * 0.6);  // Dynamic based on image height

    // Calculate line points with bounds checking
    double right_ini_x = std::abs(right_m) > 1e-6 ? ((ini_y - right_b.y) / right_m) + right_b.x : right_b.x;
    double right_fin_x = std::abs(right_m) > 1e-6 ? ((fin_y - right_b.y) / right_m) + right_b.x : right_b.x;
    double left_ini_x = std::abs(left_m) > 1e-6 ? ((ini_y - left_b.y) / left_m) + left_b.x : left_b.x;
    double left_fin_x = std::abs(left_m) > 1e-6 ? ((fin_y - left_b.y) / left_m) + left_b.x : left_b.x;

    // Clamp values to image bounds
    right_ini_x = std::max(0.0, std::min(static_cast<double>(inputImage.cols), right_ini_x));
    right_fin_x = std::max(0.0, std::min(static_cast<double>(inputImage.cols), right_fin_x));
    left_ini_x = std::max(0.0, std::min(static_cast<double>(inputImage.cols), left_ini_x));
    left_fin_x = std::max(0.0, std::min(static_cast<double>(inputImage.cols), left_fin_x));

    output[0] = cv::Point(static_cast<int>(right_ini_x), ini_y);
    output[1] = cv::Point(static_cast<int>(right_fin_x), fin_y);
    output[2] = cv::Point(static_cast<int>(left_ini_x), ini_y);
    output[3] = cv::Point(static_cast<int>(left_fin_x), fin_y);

    // Store current frame data for next frame smoothing
    prev_right_line = {output[0], output[1]};
    prev_left_line = {output[2], output[3]};

    return output;
}

// TURN PREDICTION
/**
*@brief Predict if the lane is turning left, right or if it is going straight
*@brief It is done by seeing where the vanishing point is with respect to the center of the image
*@return String that says if there is left or right turn or if the road is straight
*/
std::string LaneDetector::predictTurn() 
{
    std::string output;
    double vanish_x;
    double thr_vp = 10;

    // The vanishing point is the point where both lane boundary lines intersect
    vanish_x = static_cast<double>(((right_m*right_b.x) - (left_m*left_b.x) - right_b.y + left_b.y) / (right_m - left_m));

    // The vanishing points location determines where is the road turning
    if (vanish_x < (img_center - thr_vp))
        output = "Turn left";
    else if (vanish_x >(img_center + thr_vp))
        output = "Turn right";
    else if (vanish_x >= (img_center - thr_vp) && vanish_x <= (img_center + thr_vp))
        output = "Straight";

    return output;
}

// PLOT RESULTS
/**
*@brief This function plots both sides of the lane, the turn prediction message and a transparent polygon that covers the area inside the lane boundaries
*@param inputImage is the original captured frame
*@param lane is the vector containing the information of both lines
*@param turn is the output string containing the turn information
*@return The function returns a 0
*/
int LaneDetector::plotLane(cv::Mat inputImage, std::vector<cv::Point> lane, std::string turn) 
{
    std::vector<cv::Point> poly_points;
    cv::Mat output;

    // Create the transparent polygon for a better visualization of the lane
    inputImage.copyTo(output);
    poly_points.push_back(lane[2]);
    poly_points.push_back(lane[0]);
    poly_points.push_back(lane[1]);
    poly_points.push_back(lane[3]);
    cv::fillConvexPoly(output, poly_points, cv::Scalar(0, 0, 255), cv::LINE_AA, 0);
    cv::addWeighted(output, 0.3, inputImage, 1.0 - 0.3, 0, inputImage);

    // Plot both lines of the lane boundary
    cv::line(inputImage, lane[0], lane[1], cv::Scalar(0, 255, 255), 5, cv::LINE_AA);
    cv::line(inputImage, lane[2], lane[3], cv::Scalar(0, 255, 255), 5, cv::LINE_AA);

    // Plot the turn message
    cv::putText(inputImage, turn, cv::Point(50, 90), cv::FONT_HERSHEY_COMPLEX_SMALL, 3, cv::Scalar(0, 255, 0), 1, cv::LINE_AA);

    // Show the final output image
    cv::namedWindow("Lane", cv::WINDOW_AUTOSIZE);
    cv::imshow("Lane", inputImage);
    return 0;
}