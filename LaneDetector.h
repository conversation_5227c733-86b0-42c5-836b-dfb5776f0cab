class LaneDetector
{
private:
	double img_size;
	double img_center;
	bool left_flag = false;     // Tells us if there's left boundary of lane detected
	bool right_flag = false;    // Tells us if there's right boundary of lane detected
	cv::Point right_b;          // Members of both line equations of the lane boundaries:
	double right_m;             // y = m*x + b
	cv::Point left_b;           //
	double left_m;              //

	// 历史帧信息用于稳定性
	std::vector<cv::Point> prev_left_line;
	std::vector<cv::Point> prev_right_line;
	int frame_count = 0;

public:
	// Apply Gaussian blurring to the input Image
	cv::<PERSON> de<PERSON>(cv::Mat inputImage);

	// Filter the image to obtain only edges with improved algorithm
	cv::Mat edgeDetector(cv::Mat img_noise);

	// Color space processing for better lane detection
	cv::Mat colorThreshold(cv::Mat inputImage);

	// Mask the edges image to only care about ROI with dynamic adjustment
	cv::Mat mask(cv::Mat img_edges);

	// Detect Hough lines in masked edges image with adaptive parameters
	std::vector<cv::Vec4i> houghLines(cv::Mat img_mask);

	// Sort detected lines by their slope into right and left lines with improved filtering
	std::vector<std::vector<cv::Vec4i> > lineSeparation(std::vector<cv::Vec4i> lines, cv::Mat img_edges);

	// Get only one line for each side of the lane
	std::vector<cv::Point> regression(std::vector<std::vector<cv::Vec4i> > left_right_lines, cv::Mat inputImage);

	// Determine if the lane is turning or not by calculating the position of the vanishing point
	std::string predictTurn();

	// Plot the resultant lane and turn prediction in the frame.
	int plotLane(cv::Mat inputImage, std::vector<cv::Point> lane, std::string turn);

	// Helper function to calculate adaptive thresholds
	std::pair<double, double> calculateAdaptiveThresholds(cv::Mat image);
};