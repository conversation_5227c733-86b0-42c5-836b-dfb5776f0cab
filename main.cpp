#include <iostream>
#include <string>
#include <vector>
#include <iomanip>
#include <opencv2/opencv.hpp>
#include "LaneDetector.h"

/**
*@brief Function main that runs the main algorithm of the lane detection.
*@brief It will read a video of a car in the highway and it will output the
*@brief same video but with the plotted detected lane
*@param argc number of command line arguments
*@param argv[] command line arguments, argv[1] should be the video file path
*@return flag_plot tells if the demo has sucessfully finished
*/
int main(int argc, char* argv[])
{
    LaneDetector lanedetector;  // 定义车道线检测对象
    cv::Mat frame;
    cv::Mat img_denoise;
    cv::Mat img_edges;
    cv::Mat img_mask;
    std::vector<cv::Vec4i> lines;
    std::vector<std::vector<cv::Vec4i> > left_right_lines;
    std::vector<cv::Point> lane;
    std::string turn;
    int flag_plot = -1;         // 返回值

    // 确定要使用的视频文件
    std::string video_file;
    if (argc > 1) {
        video_file = argv[1];
    } else {
        // 默认使用简单视频，用户可以选择
        std::cout << "请选择要测试的视频文件:" << std::endl;
        std::cout << "1. video_project.mp4 (简单路面)" << std::endl;
        std::cout << "2. video_challenge.mp4 (挑战路面)" << std::endl;
        std::cout << "3. video_harder_challenge.mp4 (困难路面)" << std::endl;
        std::cout << "请输入选择 (1-3): ";

        int choice;
        std::cin >> choice;

        switch(choice) {
            case 1:
                video_file = "video_project.mp4";
                break;
            case 2:
                video_file = "video_challenge.mp4";
                break;
            case 3:
                video_file = "video_harder_challenge.mp4";
                break;
            default:
                video_file = "video_project.mp4";
                std::cout << "无效选择，使用默认视频: " << video_file << std::endl;
        }
    }

    std::cout << "正在处理视频文件: " << video_file << std::endl;

    // 打开测试视频文件
    cv::VideoCapture cap(video_file);
    if (!cap.isOpened()) {
        std::cerr << "错误: 无法打开视频文件 " << video_file << std::endl;
        std::cerr << "请确保视频文件存在于当前目录中" << std::endl;
        return -1;
    }

    // 获取视频信息
    int frame_count = static_cast<int>(cap.get(cv::CAP_PROP_FRAME_COUNT));
    double fps = cap.get(cv::CAP_PROP_FPS);
    std::cout << "视频信息: " << frame_count << " 帧, " << fps << " FPS" << std::endl;

    // 车道线检测算法主循环
    int current_frame = 0;
    int successful_detections = 0;

    std::cout << "开始处理视频帧..." << std::endl;
    std::cout << "按任意键退出" << std::endl;

    while (1)
    {
        // 读入一帧图像，不成功则退出
        if (!cap.read(frame)) {
            std::cout << "视频处理完成" << std::endl;
            break;
        }

        current_frame++;

        // 显示处理进度
        if (current_frame % 30 == 0) {
            double progress = (double)current_frame / frame_count * 100.0;
            std::cout << "处理进度: " << std::fixed << std::setprecision(1)
                      << progress << "% (" << current_frame << "/" << frame_count
                      << " 帧), 成功检测: " << successful_detections << " 帧" << std::endl;
        }

        try {
            // 采用Gaussian滤波器去噪声
            img_denoise = lanedetector.deNoise(frame);

            // 边缘检测
            img_edges = lanedetector.edgeDetector(img_denoise);

            // 裁剪图像以获取ROI
            img_mask = lanedetector.mask(img_edges);

            // 在ROI区域通过Hough变换得到Hough线
            lines = lanedetector.houghLines(img_mask);

            if (!lines.empty())
            {
                // 分离左右车道线
                left_right_lines = lanedetector.lineSeparation(lines, img_edges);

                // 采用回归法获取单边车道线
                lane = lanedetector.regression(left_right_lines, frame);

                // 预测车道线是向左、向右还是直行
                turn = lanedetector.predictTurn();

                // 在视频图上绘制车道线
                flag_plot = lanedetector.plotLane(frame, lane, turn);

                if (flag_plot == 0) {
                    successful_detections++;
                }
            }
            else
            {
                flag_plot = -1;
                // 在没有检测到线条时显示原始图像
                cv::putText(frame, "No lanes detected", cv::Point(50, 50),
                           cv::FONT_HERSHEY_COMPLEX_SMALL, 2, cv::Scalar(0, 0, 255), 2);
                cv::namedWindow("Lane", cv::WINDOW_AUTOSIZE);
                cv::imshow("Lane", frame);
            }
        }
        catch (const cv::Exception& e) {
            std::cerr << "OpenCV错误: " << e.what() << std::endl;
            flag_plot = -1;
        }
        catch (const std::exception& e) {
            std::cerr << "处理错误: " << e.what() << std::endl;
            flag_plot = -1;
        }

        // 在25ms以内等待到按键则退出
        if (cv::waitKey(25) >= 0) {
            std::cout << "用户中断处理" << std::endl;
            break;
        }
    }

    // 输出最终统计信息
    double success_rate = (double)successful_detections / current_frame * 100.0;
    std::cout << "\n处理完成!" << std::endl;
    std::cout << "总帧数: " << current_frame << std::endl;
    std::cout << "成功检测帧数: " << successful_detections << std::endl;
    std::cout << "成功率: " << std::fixed << std::setprecision(1) << success_rate << "%" << std::endl;

    return flag_plot;
}
